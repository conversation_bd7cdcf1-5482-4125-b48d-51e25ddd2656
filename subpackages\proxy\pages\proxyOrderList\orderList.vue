<template>
  <view class="order-list-container">
    <!-- 顶部导航栏 -->
    <view class="nav-header">
      <view class="nav-title">{{ type === '0' ? '已完成订单' : '退货订单' }}</view>
      <view class="nav-subtitle">{{ getDateRangeText() }}</view>
    </view>

    <!-- 统计信息卡片 -->
    <view class="stats-card">
      <view class="stats-item">
        <view class="stats-value">{{ orderList.length }}</view>
        <view class="stats-label">订单数量</view>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <view class="stats-value">{{ getTotalAmount() }} ￥</view>
        <view class="stats-label">{{ type === '0' ? '总金额' : '退款金额' }}</view>
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="content-wrapper">
      <view v-if="orderList.length === 0 && !isSend" class="empty-state">
        <view class="empty-icon">📋</view>
        <view class="empty-text">暂无订单</view>
      </view>

      <view v-if="isSend" class="loading-state">
        <view class="loading-icon">⏳</view>
        <view class="loading-text">加载中...</view>
      </view>

      <view class="order-list">
        <view class="order-card" v-for="(data, index) in orderList" :key="index">
          <!-- 订单头部 -->
          <view class="order-header">
            <view class="order-title">
              <view class="product-name">{{ dict[data.equityType] || '权益订单' }}</view>
            </view>
            <view class="order-status" :class="getStatusClass(data)">
              {{ getStatus(data) }}
            </view>
          </view>

          <!-- 订单信息 -->
          <view class="order-info">
            <view class="info-row">
              <view class="info-label">订单号</view>
              <view class="info-value-row">
                <text class="info-value">{{ data.orderno }}</text>
                <view class="copy-btn" @click="copy(data.orderno)">
                  <uni-icons type="copy" size="12" color="#00d4aa"></uni-icons>
                  <text class="copy-text">复制</text>
                </view>
              </view>
            </view>

            <view class="info-row">
              <view class="info-label">手机号</view>
              <view class="info-value">{{ data.phone }}</view>
            </view>

            <view class="info-row">
              <view class="info-label">归属</view>
              <view class="info-value">{{ data.relationship }}</view>
            </view>

            <view class="info-row">
              <view class="info-label">订单时间</view>
              <view class="info-value">{{ formatDate(data.orderTime) }}</view>
            </view>
          </view>

          <!-- 订单底部 -->
          <view class="order-footer">
            <view class="order-amount">
              <text class="amount-label">实付金额：</text>
              <text class="amount-value">￥{{ data.orderMoney }}</text>
            </view>
            <view v-if="type === '0' && data.refundStatus !== 'WAIT_REFUND'" class="refund-btn" @click="refund(data)">
              申请退款
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  getOrdersDetailsInfoByStatus,
  requestARefund,
  allcommodity,
} from "@/subpackages/proxy/api/proxyOrderList.js";
import { getUserId, isLogin, isProxyLogin } from "@/utils/auth.js";
// const dict = ['', '黄金卡', '钻石卡']
export default {
  data() {
    return {
      range: [],
      type: "",
      orderList: [],
      dict: {},
      isSend: false,
    };
  },
  methods: {
    getOrdersDetailsInfoByStatus() {
      const data = {
        userid: getUserId(),
        startTime: this.range[0] + " 00:00:00",
        endTime: this.range[1] + " 23:59:59",
        orderType: this.type,
      };
      this.isSend = true;
      getOrdersDetailsInfoByStatus(data)
        .then((res) => {
          console.log(res);
          res = res.data;
          if (res.code !== 200) {
            return;
          }
          if (this.type === "0") {
            this.orderList = res.data.allCompleteOrders;
          } else {
            this.orderList = res.data.allRefundOrders;
          }
          console.log(this.orderList);
        })
        .finally(() => {
          this.isSend = false;
        });
    },
    getStatus(obj) {
      if (!obj.refundStatus) {
        if (obj.orderStatus === "COMPLETE") {
          return "已完成";
        } else if (obj.orderStatus === "FAILED") {
          return "已失败";
        }
        return "待支付";
      }
      if (obj.refundStatus === "WAIT_REFUND") {
        return "待退款";
      } else if (obj.refundStatus === "REFUND") {
        return "已退款";
      } else if (obj.refundStatus === "REFUNDING") {
        return "退款中";
      }
      return "退款失败";
    },
    formatDate(str = "") {
      const data1 = str.split(" ");
      const data2 = data1[0].split("/");
      return data2[0] + "年" + data2[1] + "月" + data2[2] + "日" + data1[1];
    },
    refund(order) {
      if (order.refundStatus === "WAIT_REFUND") {
        uni.showToast({
          duration: 2000,
          icon: "error",
          title: "订单已申请退款",
        });
        return;
      }
      uni.showModal({
        title: "退款",
        content: "是否对该订单申请退款",
        success: (res) => {
          if (res.cancel) {
            return;
          }
          requestARefund({ orderno: order.orderno }).then((res) => {
            console.log(res);
            res = res.data;
            if (res.code !== 200) {
              uni.showToast({
                icon: "error",
                title: "申请失败",
                duration: 1500,
              });
              return;
            }
            uni.showToast({
              icon: "success",
              title: "申请成功",
              duration: 1500,
            });
            order.refundStatus = "WAIT_REFUND";
          });
        },
      });
    },
    copy(text) {
      // 复制功能
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: "复制成功",
            icon: "success",
          });
        },
        fail: () => {
          uni.showToast({
            title: "复制失败",
            icon: "none",
          });
        },
      });
    },

    allcommodity() {
      allcommodity().then((res) => {
        res = res.data;
        if (res.code !== 200) {
          return;
        }
        res.data.forEach((o) => {
          this.dict[o.id] = o.name;
        });
        this.dict = { ...this.dict };
      });
    },

    // 获取日期范围文本
    getDateRangeText() {
      if (this.range && this.range.length === 2) {
        const start = this.range[0].replaceAll('-', '/');
        const end = this.range[1].replaceAll('-', '/');
        if (start === end) {
          return start;
        }
        return `${start} 至 ${end}`;
      }
      return '';
    },

    // 获取总金额
    getTotalAmount() {
      let total = 0;
      this.orderList.forEach(order => {
        total += parseFloat(order.orderMoney || 0);
      });
      return total.toFixed(2);
    },

    // 获取状态样式类
    getStatusClass(data) {
      const status = this.getStatus(data);
      if (status === '已完成') return 'status-success';
      if (status === '已失败') return 'status-failed';
      if (status === '已退款') return 'status-refunded';
      if (status === '待退款') return 'status-pending';
      if (status === '退款中') return 'status-refunding';
      return 'status-default';
    },
  },
  onLoad(query) {
    // 检查代理端登录状态
    if (!isLogin() || !isProxyLogin()) {
      console.log("用户未登录或非代理端登录，跳转到统一登录页");
      uni.reLaunch({
        url: "/pages/unifiedLogin/unifiedLogin?mode=proxy",
      });
      return;
    }
    const data = JSON.parse(query.data);
    this.range = data.range;
    this.type = data.type;
    this.getOrdersDetailsInfoByStatus();
    this.allcommodity();
  },
};
</script>

<style lang="scss">
/* 页面容器 */
.order-list-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ededed 100%);
}

/* 顶部导航栏 */
.nav-header {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  padding: 40px 20px 30px;
  color: white;

  .nav-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
  }

  .nav-subtitle {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 400;
  }
}

/* 统计信息卡片 */
.stats-card {
  background: white;
  margin: -20px 20px 20px;
  border-radius: 20px;
  padding: 25px;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4px 20px rgba(0, 212, 170, 0.15);
  position: relative;
  z-index: 2;

  .stats-item {
    text-align: center;
    flex: 1;

    .stats-value {
      font-size: 28px;
      font-weight: 700;
      color: #00d4aa;
      margin-bottom: 8px;
      line-height: 1.2;
    }

    .stats-label {
      font-size: 13px;
      color: #666;
      font-weight: 500;
    }
  }

  .stats-divider {
    width: 1px;
    background: #e9ecef;
    margin: 0 20px;
  }
}

/* 内容区域 */
.content-wrapper {
  padding: 0 20px 20px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 16px;
    font-weight: 500;
  }
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;

  .loading-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
  }

  .loading-text {
    font-size: 16px;
    font-weight: 500;
  }
}

/* 订单列表 */
.order-list {
  .order-card {
    background: white;
    border-radius: 16px;
    margin-bottom: 16px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }

    /* 订单头部 */
    .order-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 20px 16px;
      border-bottom: 1px solid #f5f5f5;

      .order-title {
        .product-name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          line-height: 1.4;
        }
      }

      .order-status {
        padding: 6px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;

        &.status-success {
          background: #e8f5e8;
          color: #52c41a;
        }

        &.status-failed {
          background: #fff2f0;
          color: #ff4d4f;
        }

        &.status-refunded {
          background: #f0f5ff;
          color: #1890ff;
        }

        &.status-pending {
          background: #fff7e6;
          color: #fa8c16;
        }

        &.status-refunding {
          background: #f6ffed;
          color: #52c41a;
        }

        &.status-default {
          background: #f5f5f5;
          color: #666;
        }
      }
    }

    /* 订单信息 */
    .order-info {
      padding: 0 20px;

      .info-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid #f8f9fa;

        &:last-child {
          border-bottom: none;
        }

        .info-label {
          font-size: 14px;
          color: #666;
          font-weight: 500;
          min-width: 80px;
        }

        .info-value {
          font-size: 14px;
          color: #333;
          font-weight: 500;
          flex: 1;
          text-align: right;
        }

        .info-value-row {
          display: flex;
          align-items: center;
          flex: 1;
          justify-content: flex-end;

          .info-value {
            margin-right: 12px;
            text-align: left;
            flex: 1;
          }

          .copy-btn {
            display: flex;
            align-items: center;
            padding: 4px 8px;
            background: #f0f9ff;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;

            &:active {
              transform: scale(0.95);
              background: #e0f2fe;
            }

            .copy-text {
              margin-left: 4px;
              font-size: 12px;
              color: #00d4aa;
              font-weight: 500;
            }
          }
        }
      }
    }

    /* 订单底部 */
    .order-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px 20px;
      background: #fafbfc;

      .order-amount {
        .amount-label {
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }

        .amount-value {
          font-size: 16px;
          color: #00d4aa;
          font-weight: 700;
          margin-left: 4px;
        }
      }

      .refund-btn {
        background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 13px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 212, 170, 0.3);

        &:active {
          transform: scale(0.95);
          box-shadow: 0 1px 4px rgba(0, 212, 170, 0.4);
        }
      }
    }
  }
}
</style>
